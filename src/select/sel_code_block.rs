use crate::md_elem::elem::*;
use crate::select::match_selector::MatchSelector;
use crate::select::string_matcher::{StringMatchError, StringMatcher};
use crate::select::CodeBlockMatcher;

#[derive(Debug, PartialEq)]
pub(crate) struct CodeBlockSelector {
    lang_matcher: StringMatcher,
    contents_matcher: StringMatcher,
}

impl From<CodeBlockMatcher> for CodeBlockSelector {
    fn from(value: CodeBlockMatcher) -> Self {
        Self {
            lang_matcher: value.language.into(),
            contents_matcher: value.contents.into(),
        }
    }
}

impl MatchSelector<CodeBlock> for CodeBlockSelector {
    const NAME: &'static str = "code block";

    fn matches(&self, code_block: &CodeBlock) -> Result<bool, StringMatchError> {
        let lang_matches = match &code_block.variant {
            CodeVariant::Code(code_opts) => {
                let actual_lang = match code_opts {
                    Some(co) => &co.language,
                    None => "",
                };
                self.lang_matcher.matches(actual_lang)?
            }
            CodeVariant::Math { .. } => false,
        };
        Ok(lang_matches && self.contents_matcher.matches(&code_block.value)?)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::md_elem::{MdContext, MdElem};
    use crate::select::{MatchReplace, Matcher, Select, TrySelector};

    // Tests for language matching (no replacement)
    #[test]
    fn language_match_only_hits() {
        let code_block_matcher = CodeBlockMatcher {
            language: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "rust".to_string(),
                    anchor_end: false,
                },
                replacement: None,
            },
            contents: MatchReplace {
                matcher: Matcher::Any { explicit: false },
                replacement: None,
            },
        };

        let code_block_selector = CodeBlockSelector::from(code_block_matcher);
        let result = code_block_selector.try_select(&MdContext::default(), code_block(Some("rust"))).unwrap();

        assert_eq!(
            result,
            Select::Hit(vec![MdElem::CodeBlock(code_block(Some("rust")))])
        );
    }

    #[test]
    fn language_match_only_misses() {
        let code_block_matcher = CodeBlockMatcher {
            language: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "javascript".to_string(),
                    anchor_end: false,
                },
                replacement: None,
            },
            contents: MatchReplace {
                matcher: Matcher::Any { explicit: false },
                replacement: None,
            },
        };

        let code_block_selector = CodeBlockSelector::from(code_block_matcher);
        let result = code_block_selector.try_select(&MdContext::default(), code_block(Some("rust"))).unwrap();

        assert_eq!(
            result,
            Select::Miss(MdElem::CodeBlock(code_block(Some("rust"))))
        );
    }

    // Tests for contents matching (no replacement)
    #[test]
    fn contents_match_only_hits() {
        let code_block_matcher = CodeBlockMatcher {
            language: MatchReplace {
                matcher: Matcher::Any { explicit: false },
                replacement: None,
            },
            contents: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "main".to_string(),
                    anchor_end: false,
                },
                replacement: None,
            },
        };

        let code_block_selector = CodeBlockSelector::from(code_block_matcher);
        let result = code_block_selector.try_select(&MdContext::default(), code_block(Some("rust"))).unwrap();

        assert_eq!(
            result,
            Select::Hit(vec![MdElem::CodeBlock(code_block(Some("rust")))])
        );
    }

    #[test]
    fn contents_match_only_misses() {
        let code_block_matcher = CodeBlockMatcher {
            language: MatchReplace {
                matcher: Matcher::Any { explicit: false },
                replacement: None,
            },
            contents: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "println".to_string(),
                    anchor_end: false,
                },
                replacement: None,
            },
        };

        let code_block_selector = CodeBlockSelector::from(code_block_matcher);
        let result = code_block_selector.try_select(&MdContext::default(), code_block(Some("rust"))).unwrap();

        assert_eq!(
            result,
            Select::Miss(MdElem::CodeBlock(code_block(Some("rust"))))
        );
    }

    // Tests for language replacement (should fail until implemented)
    #[test]
    fn language_replacement_with_match_should_fail() {
        let code_block_matcher = CodeBlockMatcher {
            language: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "rust".to_string(),
                    anchor_end: false,
                },
                replacement: Some("python".to_string()),
            },
            contents: MatchReplace {
                matcher: Matcher::Any { explicit: false },
                replacement: None,
            },
        };

        let code_block_selector = CodeBlockSelector::from(code_block_matcher);
        let result = code_block_selector.try_select(&MdContext::default(), code_block(Some("rust")));

        // This should fail until replacement is implemented
        assert!(result.is_err(), "Expected error for unimplemented replacement, got: {:?}", result);
    }

    #[test]
    fn language_replacement_with_miss_should_succeed() {
        let code_block_matcher = CodeBlockMatcher {
            language: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "javascript".to_string(),
                    anchor_end: false,
                },
                replacement: Some("python".to_string()),
            },
            contents: MatchReplace {
                matcher: Matcher::Any { explicit: false },
                replacement: None,
            },
        };

        let code_block_selector = CodeBlockSelector::from(code_block_matcher);
        let result = code_block_selector.try_select(&MdContext::default(), code_block(Some("rust"))).unwrap();

        // This should succeed (miss) even with replacement since there's no match
        assert_eq!(
            result,
            Select::Miss(MdElem::CodeBlock(code_block(Some("rust"))))
        );
    }

    // Tests for contents replacement (should fail until implemented)
    #[test]
    fn contents_replacement_with_match_should_fail() {
        let code_block_matcher = CodeBlockMatcher {
            language: MatchReplace {
                matcher: Matcher::Any { explicit: false },
                replacement: None,
            },
            contents: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "main".to_string(),
                    anchor_end: false,
                },
                replacement: Some("start".to_string()),
            },
        };

        let code_block_selector = CodeBlockSelector::from(code_block_matcher);
        let result = code_block_selector.try_select(&MdContext::default(), code_block(Some("rust")));

        // This should fail until replacement is implemented
        assert!(result.is_err(), "Expected error for unimplemented replacement, got: {:?}", result);
    }

    #[test]
    fn contents_replacement_with_miss_should_succeed() {
        let code_block_matcher = CodeBlockMatcher {
            language: MatchReplace {
                matcher: Matcher::Any { explicit: false },
                replacement: None,
            },
            contents: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "println".to_string(),
                    anchor_end: false,
                },
                replacement: Some("print".to_string()),
            },
        };

        let code_block_selector = CodeBlockSelector::from(code_block_matcher);
        let result = code_block_selector.try_select(&MdContext::default(), code_block(Some("rust"))).unwrap();

        // This should succeed (miss) even with replacement since there's no match
        assert_eq!(
            result,
            Select::Miss(MdElem::CodeBlock(code_block(Some("rust"))))
        );
    }

    // Tests for both language and contents replacement
    #[test]
    fn both_replacement_with_match_should_fail() {
        let code_block_matcher = CodeBlockMatcher {
            language: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "rust".to_string(),
                    anchor_end: false,
                },
                replacement: Some("python".to_string()),
            },
            contents: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "main".to_string(),
                    anchor_end: false,
                },
                replacement: Some("start".to_string()),
            },
        };

        let code_block_selector = CodeBlockSelector::from(code_block_matcher);
        let result = code_block_selector.try_select(&MdContext::default(), code_block(Some("rust")));

        // This should fail until replacement is implemented
        assert!(result.is_err(), "Expected error for unimplemented replacement, got: {:?}", result);
    }

    // Tests for edge cases
    #[test]
    fn no_language_code_block_language_match() {
        let code_block_matcher = CodeBlockMatcher {
            language: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "rust".to_string(),
                    anchor_end: false,
                },
                replacement: None,
            },
            contents: MatchReplace {
                matcher: Matcher::Any { explicit: false },
                replacement: None,
            },
        };

        let code_block_selector = CodeBlockSelector::from(code_block_matcher);
        let result = code_block_selector.try_select(&MdContext::default(), code_block(None)).unwrap();

        // Should miss since there's no language to match against
        assert_eq!(
            result,
            Select::Miss(MdElem::CodeBlock(code_block(None)))
        );
    }

    #[test]
    fn math_block_never_matches_language() {
        let code_block_matcher = CodeBlockMatcher {
            language: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "math".to_string(),
                    anchor_end: false,
                },
                replacement: None,
            },
            contents: MatchReplace {
                matcher: Matcher::Text {
                    case_sensitive: false,
                    anchor_start: false,
                    text: "x".to_string(),
                    anchor_end: false,
                },
                replacement: None,
            },
        };

        let math_block = CodeBlock {
            variant: CodeVariant::Math { metadata: Some("math".to_string()) },
            value: "x = 1".to_string(),
        };

        let code_block_selector = CodeBlockSelector::from(code_block_matcher);
        let result = code_block_selector.try_select(&MdContext::default(), math_block.clone()).unwrap();

        // Should hit on contents but not language (math blocks don't have language matching)
        assert_eq!(
            result,
            Select::Hit(vec![MdElem::CodeBlock(math_block)])
        );
    }


    // Helper function to create a code block with optional language
    fn code_block(language: Option<&str>) -> CodeBlock {
        CodeBlock {
            variant: CodeVariant::Code(language.map(|lang| CodeOpts {
                language: lang.to_string(),
                metadata: None,
            })),
            value: "fn main() {}".to_string(),
        }
    }
}
